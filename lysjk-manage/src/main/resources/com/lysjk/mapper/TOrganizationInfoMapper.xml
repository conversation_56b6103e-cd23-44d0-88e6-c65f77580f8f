<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lysjk.mapper.TOrganizationInfoMapper">
    <insert id="batchInsert">
        insert into t_organization_info (name, create_dt, create_by, update_dt, update_by, remark)
        values
        <foreach collection="organizationInfoList" item="item" separator=",">
            (#{item.name}, #{item.createDt}, #{item.createBy}, #{item.updateDt}, #{item.updateBy}, #{item.remark})
        </foreach>
    </insert>
    <!--用到了in,所以有括号-->
  <delete id="deleteByIds">
    delete from t_organization_info where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
  <select id="pageQuery" resultType="com.lysjk.entity.TOrganizationInfo">
    select * from t_organization_info
    <where>
      <if test="name != null and name != ''">
        and name like concat('%', #{name}, '%')
      </if>
    </where>
    order by create_dt desc
  </select>
</mapper>