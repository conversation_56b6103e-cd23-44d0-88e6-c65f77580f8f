<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lysjk.mapper.TMonitoringRecordMapper">

  <!-- 基础结果映射 -->
  <resultMap id="BaseResultMap" type="com.lysjk.entity.TMonitoringRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="point_measure" jdbcType="VARCHAR" property="pointMeasure" />
    <result column="wind_direction_speed" jdbcType="VARCHAR" property="windDirectionSpeed" />
    <result column="spectral_measure" jdbcType="VARCHAR" property="spectralMeasure" />
    <result column="transparency_measure" jdbcType="VARCHAR" property="transparencyMeasure" />
    <result column="turbidity_measure" jdbcType="VARCHAR" property="turbidityMeasure" />
    <result column="depth_measure" jdbcType="VARCHAR" property="depthMeasure" />
    <result column="water_color_photo" jdbcType="VARCHAR" property="waterColorPhoto" />
    <result column="laboratory_test" jdbcType="VARCHAR" property="laboratoryTest" />
    <result column="create_dt" jdbcType="TIMESTAMP" property="createDt" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="update_dt" jdbcType="TIMESTAMP" property="updateDt" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="point_id" jdbcType="INTEGER" property="pointId" />
    <result column="region_id" jdbcType="INTEGER" property="regionId" />
  </resultMap>

  <!-- 基础字段列表 -->
  <sql id="Base_Column_List">
    id, point_measure, wind_direction_speed, spectral_measure, transparency_measure,
    turbidity_measure, depth_measure, water_color_photo, laboratory_test,
    create_dt, create_by, update_dt, update_by, remark, point_id, region_id
  </sql>

  <!-- 新增测量记录 -->
  <insert id="insert" useGeneratedKeys="true" keyProperty="id">
    insert into t_monitoring_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pointMeasure != null and pointMeasure != ''">point_measure,</if>
      <if test="windDirectionSpeed != null and windDirectionSpeed != ''">wind_direction_speed,</if>
      <if test="spectralMeasure != null and spectralMeasure != ''">spectral_measure,</if>
      <if test="transparencyMeasure != null and transparencyMeasure != ''">transparency_measure,</if>
      <if test="turbidityMeasure != null and turbidityMeasure != ''">turbidity_measure,</if>
      <if test="depthMeasure != null and depthMeasure != ''">depth_measure,</if>
      <if test="waterColorPhoto != null and waterColorPhoto != ''">water_color_photo,</if>
      <if test="laboratoryTest != null and laboratoryTest != ''">laboratory_test,</if>
      <if test="createDt != null">create_dt,</if>
      <if test="createBy != null">create_by,</if>
      <if test="updateDt != null">update_dt,</if>
      <if test="updateBy != null">update_by,</if>
      <if test="remark != null and remark != ''">remark,</if>
      <if test="pointId != null">point_id,</if>
      <if test="regionId != null">region_id,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pointMeasure != null and pointMeasure != ''">#{pointMeasure},</if>
      <if test="windDirectionSpeed != null and windDirectionSpeed != ''">#{windDirectionSpeed},</if>
      <if test="spectralMeasure != null and spectralMeasure != ''">#{spectralMeasure},</if>
      <if test="transparencyMeasure != null and transparencyMeasure != ''">#{transparencyMeasure},</if>
      <if test="turbidityMeasure != null and turbidityMeasure != ''">#{turbidityMeasure},</if>
      <if test="depthMeasure != null and depthMeasure != ''">#{depthMeasure},</if>
      <if test="waterColorPhoto != null and waterColorPhoto != ''">#{waterColorPhoto},</if>
      <if test="laboratoryTest != null and laboratoryTest != ''">#{laboratoryTest},</if>
      <if test="createDt != null">#{createDt},</if>
      <if test="createBy != null">#{createBy},</if>
      <if test="updateDt != null">#{updateDt},</if>
      <if test="updateBy != null">#{updateBy},</if>
      <if test="remark != null and remark != ''">#{remark},</if>
      <if test="pointId != null">#{pointId},</if>
      <if test="regionId != null">#{regionId},</if>
    </trim>
  </insert>

  <!-- 批量删除测量记录 -->
  <delete id="deleteByIds">
    delete from t_monitoring_record where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <!-- 更新测量记录 -->
  <update id="update">
    update t_monitoring_record
    <set>
      <if test="pointMeasure != null and pointMeasure != ''">point_measure = #{pointMeasure},</if>
      <if test="windDirectionSpeed != null and windDirectionSpeed != ''">wind_direction_speed = #{windDirectionSpeed},</if>
      <if test="spectralMeasure != null and spectralMeasure != ''">spectral_measure = #{spectralMeasure},</if>
      <if test="transparencyMeasure != null and transparencyMeasure != ''">transparency_measure = #{transparencyMeasure},</if>
      <if test="turbidityMeasure != null and turbidityMeasure != ''">turbidity_measure = #{turbidityMeasure},</if>
      <if test="depthMeasure != null and depthMeasure != ''">depth_measure = #{depthMeasure},</if>
      <if test="waterColorPhoto != null and waterColorPhoto != ''">water_color_photo = #{waterColorPhoto},</if>
      <if test="laboratoryTest != null and laboratoryTest != ''">laboratory_test = #{laboratoryTest},</if>
      <if test="updateDt != null">update_dt = #{updateDt},</if>
      <if test="updateBy != null">update_by = #{updateBy},</if>
      <if test="remark != null and remark != ''">remark = #{remark},</if>
      <if test="pointId != null">point_id = #{pointId},</if>
      <if test="regionId != null">region_id = #{regionId},</if>
    </set>
    where id = #{id}
  </update>

  <!-- 分页查询测量记录 -->
  <select id="pageQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_monitoring_record
    <where>
      <if test="pointId != null">
        and point_id = #{pointId}
      </if>
      <if test="regionId != null">
        and region_id = #{regionId}
      </if>
      <if test="pointMeasure != null and pointMeasure != ''">
        and point_measure like concat('%', #{pointMeasure}, '%')
      </if>
      <if test="windDirectionSpeed != null and windDirectionSpeed != ''">
        and wind_direction_speed like concat('%', #{windDirectionSpeed}, '%')
      </if>
      <if test="spectralMeasure != null and spectralMeasure != ''">
        and spectral_measure like concat('%', #{spectralMeasure}, '%')
      </if>
      <if test="transparencyMeasure != null and transparencyMeasure != ''">
        and transparency_measure like concat('%', #{transparencyMeasure}, '%')
      </if>
      <if test="turbidityMeasure != null and turbidityMeasure != ''">
        and turbidity_measure like concat('%', #{turbidityMeasure}, '%')
      </if>
      <if test="depthMeasure != null and depthMeasure != ''">
        and depth_measure like concat('%', #{depthMeasure}, '%')
      </if>
      <if test="waterColorPhoto != null and waterColorPhoto != ''">
        and water_color_photo like concat('%', #{waterColorPhoto}, '%')
      </if>
      <if test="laboratoryTest != null and laboratoryTest != ''">
        and laboratory_test like concat('%', #{laboratoryTest}, '%')
      </if>
      <if test="remark != null and remark != ''">
        and remark like concat('%', #{remark}, '%')
      </if>
    </where>
    order by create_dt desc
  </select>

</mapper>
