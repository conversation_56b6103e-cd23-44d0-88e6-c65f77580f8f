package com.lysjk.service;

import com.github.pagehelper.PageInfo;
import com.lysjk.dto.TRegionInfoPageQueryDTO;
import com.lysjk.dto.TRegionInfoWithMonitoringPointsDTO;
import com.lysjk.entity.TRegionInfo;
import com.lysjk.result.PageResult;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.MultiPolygon;

import java.util.List;

/**
 * 地物信息服务接口
 */
public interface TRegionInfoService {

    /**
     * 新增地物信息
     * @param regionInfo 地物信息
     */
    void save(TRegionInfo regionInfo);

    /**
     * 批量删除地物信息
     * @param ids ID列表
     */
    void deleteBatch(List<Integer> ids);

    /**
     * 更新地物信息
     * @param regionInfo 地物信息
     */
    void update(TRegionInfo regionInfo);

    /**
     * 根据主键查询地物信息
     * @param id 地物ID
     * @return 地物信息
     */
    TRegionInfo selectByPrimaryKey(Integer id);
    
    /**
     * 根据行政区划条件查询地物信息
     * @param sheng 省名称（可为null）
     * @param shi 市名称（可为null）
     * @param qu 区县名称（可为null）
     * @param zhen 街镇名称（可为null）
     * @return 地物信息列表
     */
    List<TRegionInfo> selectByAdministrativeRegion(String sheng, String shi, String qu, String zhen);
    
    /**
     * 根据ID查询中心点坐标
     * @param id 地物ID
     * @return 中心点坐标
     */
    Point selectCenterById(Integer id);
    
    /**
     * 根据ID查询区域范围
     * @param id 地物ID
     * @return 区域范围
     */
    MultiPolygon selectRegionById(Integer id);
    
    /**
     * 新增地物信息
     * @param record 地物信息
     * @return 影响行数
     */
    int insert(TRegionInfo record);
    
    /**
     * 选择性新增地物信息
     * @param record 地物信息
     * @return 影响行数
     */
    int insertSelective(TRegionInfo record);
    
    /**
     * 根据主键删除地物信息
     * @param id 地物ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Integer id);
    
    /**
     * 更新中心点坐标
     * @param id 地物ID
     * @param center 新的中心点坐标
     * @return 影响行数
     */
    int updateCenterById(Integer id, Point center);
    
    /**
     * 更新区域范围
     * @param id 地物ID
     * @param region 新的区域范围
     * @return 影响行数
     */
    int updateRegionById(Integer id, MultiPolygon region);
    
    /**
     * 更新业务字段（排除系统字段）
     * @param record 地物信息对象
     * @return 影响行数
     */
    int updateBusinessFields(TRegionInfo record);

    /**
     * 分页条件查询地物信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    PageResult selectPage(TRegionInfoPageQueryDTO pageQueryDTO);
    

    
    /**
     * 根据主键全量更新
     * @param record 地物信息
     * @return 影响行数
     */
    int updateByPrimaryKey(TRegionInfo record);

    /**
     * 查询地物信息及其关联的监测点列表
     * @return 地物信息与监测点关联查询结果列表
     */
    List<TRegionInfoWithMonitoringPointsDTO> selectRegionInfoWithMonitoringPoints();

    /**
     * 根据地物ID查询地物信息及其关联的监测点列表
     * @param regionId 地物ID
     * @return 地物信息与监测点关联查询结果
     */
    List<TRegionInfoWithMonitoringPointsDTO> selectRegionInfoWithMonitoringPointsById(Integer regionId);

    /**
     * 分页查询地物信息及其关联的监测点列表
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    PageInfo<TRegionInfoWithMonitoringPointsDTO> selectRegionInfoWithMonitoringPointsPage(int pageNum, int pageSize);

    /**
     * 根据地物名称查询地物信息
     * @param name 地物名称
     * @return 地物信息，不存在返回null
     */
    TRegionInfo selectByName(String name);

    /**
     * 检查地物编码唯一性（简化版本）
     * @param code 地物编码
     */
    void checkCodeUnique(String code);
}
