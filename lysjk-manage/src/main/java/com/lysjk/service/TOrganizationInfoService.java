package com.lysjk.service;

import com.lysjk.dto.TOrganizationInfoPageQueryDTO;
import com.lysjk.entity.TOrganizationInfo;
import com.lysjk.result.PageResult;

import java.util.List;

public interface TOrganizationInfoService {
    /**
     * 新增单位信息
     * @param organizationInfo
     */
    void save(TOrganizationInfo organizationInfo);

    /**
     * 批量删除操作
     * @param ids
     */
    void deleteBath(List<Long> ids);

    /**
     * 更新单位信息
     * @param organizationInfo
     */
    void update(TOrganizationInfo organizationInfo);

    /**
     * 根据名称条件分页查询
     * @param organizationInfoPageQueryDTO
     * @return
     */
    PageResult selectPage(TOrganizationInfoPageQueryDTO organizationInfoPageQueryDTO);

    /**
     * 检查单位名称唯一性（简化版本）
     * @param name 单位名称
     */
    void checkNameUnique(String name);

    /**
     * 根据单位名称来查询单位ID
     */
    Integer selectByExcelName(String name);

    /**
     * 批量新增单位信息
     */
    void batchInsert(List<TOrganizationInfo> organizationInfo);

}
