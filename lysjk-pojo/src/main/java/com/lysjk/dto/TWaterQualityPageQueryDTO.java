package com.lysjk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lysjk.config.serializer.FlexibleLocalDateTimeDeserializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class TWaterQualityPageQueryDTO implements Serializable {

    //页码(先默认设值)
    private int page = 1;

    //每页显示记录数
    private int pageSize = 10;

    /**
     * 关联监测点ID
     * TODO 后续我来处理
     */
    private Integer pointId;

    /**
     * 关联所属地物ID
     * TODO 后续我来处理
     */
    private Integer regionId;

    /**
     * 叶绿素
     */
    private Double chlorophyll;

    /**
     * 有机悬浮物
     */
    private Double tssOrganic;

    /**
     * 无机悬浮物
     */
    private Double tssInorganic;

    /**
     * 总悬浮物
     */
    private Double tss;

    /**
     * 透明度
     */
    private Double transparency;

    /**
     * 水体浑浊度
     */
    private Double turbidity;

    /**
     * 水温
     */
    private Double waterTemperature;

    /**
     * PH值
     */
    private Double ph;

    /**
     * TN
     */
    private Double tn;

    /**
     * TP
     */
    private Double tp;

    /**
     * 电导率
     */
    private Double specificConductance;

    /**
     * 溶解氧
     */
    private Double dissolvedOxyg;

    /**
     * 高锰酸盐指数
     */
    private Double permanganateIndex;

    /**
     * 氨氮
     */
    private Double ammoniaNitrogen;

    /**
     * 亚硝态氮
     */
    private Double nitriteNitrogen;

    /**
     * 硝态氮
     */
    private Double nitrateNitrogen;

    /**
     * 各种溶解的有机物含量
     * CDOM(400NM)
     */
    private Double cdom400;

    /**
     * CDOM(440NM)
     */
    private Double cdom440;

    /**
     * CDOM(480NM)
     */
    private Double cdom480;

    /**
     * Rrs689/Rrs675
     */
    private Double rrs689675;

    /**
     * 氟离子
     */
    private Double fluorinion;

    /**
     * 氯离子
     */
    private Double chloridion;

    /**
     * 硫酸根离子
     */
    private Double sulfateIon;

    /**
     * 监测单位ID
     * TODO 后续我来处理
     */
    private Integer orgnizationId;

    /**
     * 黄色物质,新添加的字段
     */
    private Double yellowSubstance;

    /**
     * 拓展|测量工具和人员
     * json格式,用JSON字符串存储在postgresqlJSON字符串,然后String类型来接受
     * 数据库传递的形式类似{"王超": "仪器1", "杨旭": "集思宝A8", "靳兴浩":"赛氏盘","胡军周":"仪器填写完整"}
     */
    private String extension;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime monitoringDt;

    private static final long serialVersionUID = 1L;
}
