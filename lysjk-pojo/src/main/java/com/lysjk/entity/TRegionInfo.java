package com.lysjk.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lysjk.config.serializer.FlexibleLocalDateTimeDeserializer;
import com.lysjk.config.serializer.MultiPolygonDeserializer;
import com.lysjk.config.serializer.MultiPolygonSerializer;
import com.lysjk.config.serializer.PointDeserializer;
import com.lysjk.config.serializer.PointSerializer;
import lombok.Data;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.MultiPolygon;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 地物信息主表
 * t_region_info
 * 目前23个字段
 */
@Data
public class TRegionInfo implements Serializable {
    /**
     * 地物ID
     */
    private Integer id;

    /**
     * 地物编码(市加区县组合)
     */
    private String code;

    /**
     * 地物名称
     */
    private String name;

    /**
     * 省名称
     */
    private String sheng;

    /**
     * 市名称
     */
    private String shi;

    /**
     * 区县名称
     */
    private String qu;

    /**
     * 街镇名称
     */
    private String zhen;

    /**
     * 坐落位置
     */
    private String zuoluo;

    /**
     * 中心点坐标 - PostGIS POINT类型
     */
    @JsonSerialize(using = PointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    private Point center;

    /**
     * 范围坐标串 - PostGIS MULTIPOLYGON类型
     */
    @JsonSerialize(using = MultiPolygonSerializer.class)
    @JsonDeserialize(using = MultiPolygonDeserializer.class)
    private MultiPolygon region;

    /**
     * 监测单位ID
     */
    private Integer organizationId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt;

    /**
     * 记录创建人ID
     */
    private Integer createBy;

    /**
     * 记录更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt;

    /**
     * 记录更新人ID
     */
    private Integer updateBy;

    /**************************补充以下字段*****************************/

    /**
     * 是否包含 1:包含 0:未包含
     */
    private Integer status;

    /**
     * 所属河系
     */
    private String river;

    /**
     * 采样时间 年月日即可(但由于数据库格式问题不能直接存年月日,所以这个展示就交给前端了)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime sampleDt;

    /**
     * 编号
     */
    private String number;

    /**
     * 采集方法
     */
    private String method;

    /**
     * 完成单位
     */
    private String unit;

    /**
     * 行政编码
     */
    private String administrativeCode;

    private static final long serialVersionUID = 1L;
}