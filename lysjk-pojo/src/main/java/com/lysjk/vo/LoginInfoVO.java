package com.lysjk.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 封装前端登录信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoginInfoVO implements Serializable {
    private Integer uid;
    private String username; // 账号具有唯一性
    private String nickname; // 暂时先没设置唯一
    private String role;
    private String token;

    private static final long serialVersionUID = 1L;
}
